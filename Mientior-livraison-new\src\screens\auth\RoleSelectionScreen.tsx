import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Animated,
  Dimensions,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuthStore } from '../../store/authStore';
import { theme } from '../../constants/theme';
import { UserRole } from '../../types';

const { width, height } = Dimensions.get('window');

interface RoleOption {
  role: UserRole;
  title: string;
  description: string;
  icon: string;
  color: string;
  features: string[];
}

const roleOptions: RoleOption[] = [
  {
    role: 'client',
    title: 'Client',
    description: 'Commandez vos repas et produits favoris',
    icon: '🛍️',
    color: theme.colors.primary[500],
    features: [
      'Commander des repas',
      'Suivre vos livraisons',
      'Gérer vos adresses',
      'Historique des commandes'
    ]
  },
  {
    role: 'livreur',
    title: 'Livreur',
    description: '<PERSON><PERSON> et gagnez de l\'argent',
    icon: '🏍️',
    color: theme.colors.secondary[500],
    features: [
      'Accepter des livraisons',
      'Navigation GPS intégrée',
      'Suivi des gains',
      'Horaires flexibles'
    ]
  },
  {
    role: 'marchand',
    title: 'Commerçant',
    description: 'Gérez votre restaurant ou boutique',
    icon: '🏪',
    color: theme.colors.accent[500],
    features: [
      'Gérer votre catalogue',
      'Recevoir des commandes',
      'Analyser vos ventes',
      'Configurer vos horaires'
    ]
  },
  {
    role: 'admin',
    title: 'Administrateur',
    description: 'Supervisez la plateforme',
    icon: '👨‍💼',
    color: theme.colors.neutral[700],
    features: [
      'Tableau de bord global',
      'Gestion des utilisateurs',
      'Statistiques avancées',
      'Configuration système'
    ]
  }
];

export const RoleSelectionScreen: React.FC = () => {
  const navigation = useNavigation();
  const { updateProfile, user } = useAuthStore();
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [loading, setLoading] = useState(false);

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnims = useRef(roleOptions.map(() => new Animated.Value(0.9))).current;

  useEffect(() => {
    // Animation d'entrée
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      ...scaleAnims.map((anim, index) =>
        Animated.spring(anim, {
          toValue: 1,
          delay: index * 100,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        })
      ),
    ]).start();
  }, [fadeAnim, slideAnim, scaleAnims]);

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role);
    
    // Animation de sélection
    const selectedIndex = roleOptions.findIndex(option => option.role === role);
    Animated.spring(scaleAnims[selectedIndex], {
      toValue: 1.05,
      tension: 100,
      friction: 5,
      useNativeDriver: true,
    }).start();

    // Réinitialiser les autres animations
    scaleAnims.forEach((anim, index) => {
      if (index !== selectedIndex) {
        Animated.spring(anim, {
          toValue: 1,
          tension: 100,
          friction: 5,
          useNativeDriver: true,
        }).start();
      }
    });
  };

  const handleContinue = async () => {
    if (!selectedRole || !user) {
      Alert.alert('Erreur', 'Veuillez sélectionner un rôle pour continuer');
      return;
    }

    try {
      setLoading(true);
      
      // Mettre à jour le profil utilisateur avec le rôle sélectionné
      await updateProfile({ role: selectedRole });
      
      // Navigation vers l'interface appropriée sera gérée par AppNavigator
      // car il écoute les changements du store auth
      
    } catch (error) {
      console.error('Erreur lors de la mise à jour du rôle:', error);
      Alert.alert(
        'Erreur',
        'Une erreur est survenue lors de la sélection du rôle. Veuillez réessayer.'
      );
    } finally {
      setLoading(false);
    }
  };

  const renderRoleCard = (option: RoleOption, index: number) => (
    <Animated.View
      key={option.role}
      style={[
        {
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnims[index] }
          ]
        }
      ]}
    >
      <TouchableOpacity
        style={[
          styles.roleCard,
          selectedRole === option.role && [
            styles.selectedCard,
            { borderColor: option.color }
          ]
        ]}
        onPress={() => handleRoleSelect(option.role)}
        activeOpacity={0.8}
      >
        <View style={styles.roleHeader}>
          <View style={[styles.iconContainer, { backgroundColor: option.color }]}>
            <Text style={styles.roleIcon}>{option.icon}</Text>
          </View>
          <View style={styles.roleInfo}>
            <Text style={styles.roleTitle}>{option.title}</Text>
            <Text style={styles.roleDescription}>{option.description}</Text>
          </View>
          {selectedRole === option.role && (
            <View style={styles.checkmark}>
              <Text style={styles.checkmarkIcon}>✓</Text>
            </View>
          )}
        </View>
        
        <View style={styles.featuresContainer}>
          {option.features.map((feature, featureIndex) => (
            <View key={featureIndex} style={styles.featureItem}>
              <Text style={styles.featureBullet}>•</Text>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={theme.colors.primary[500]} barStyle="light-content" />
      
      {/* Header */}
      <Animated.View 
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}
      >
        <Text style={styles.headerTitle}>Choisissez votre rôle</Text>
        <Text style={styles.headerSubtitle}>
          Sélectionnez comment vous souhaitez utiliser Mientior
        </Text>
      </Animated.View>

      {/* Contenu principal */}
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {roleOptions.map((option, index) => renderRoleCard(option, index))}
      </ScrollView>

      {/* Bouton de confirmation */}
      <Animated.View 
        style={[
          styles.footer,
          { opacity: fadeAnim }
        ]}
      >
        <TouchableOpacity
          style={[
            styles.continueButton,
            (!selectedRole || loading) && styles.disabledButton
          ]}
          onPress={handleContinue}
          disabled={!selectedRole || loading}
          activeOpacity={0.8}
        >
          <Text style={[
            styles.continueButtonText,
            (!selectedRole || loading) && styles.disabledButtonText
          ]}>
            {loading ? 'Chargement...' : 'Continuer'}
          </Text>
        </TouchableOpacity>
        
        <Text style={styles.footerNote}>
          Vous pourrez changer de rôle plus tard dans les paramètres
        </Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  header: {
    backgroundColor: theme.colors.primary[500],
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text.inverse,
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: theme.colors.text.inverse,
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 24,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 24,
    paddingBottom: 100,
  },
  roleCard: {
    backgroundColor: theme.colors.surface.primary,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: theme.colors.border.light,
    ...theme.shadows.base,
  },
  selectedCard: {
    borderWidth: 2,
    ...theme.shadows.md,
  },
  roleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  roleIcon: {
    fontSize: 28,
  },
  roleInfo: {
    flex: 1,
  },
  roleTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  roleDescription: {
    fontSize: 14,
    color: theme.colors.text.secondary,
    lineHeight: 20,
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.success,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkIcon: {
    color: theme.colors.text.inverse,
    fontSize: 14,
    fontWeight: 'bold',
  },
  featuresContainer: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  featureBullet: {
    color: theme.colors.primary[500],
    fontSize: 16,
    marginRight: 8,
    fontWeight: 'bold',
  },
  featureText: {
    fontSize: 14,
    color: theme.colors.text.secondary,
    flex: 1,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surface.primary,
    padding: 24,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border.light,
  },
  continueButton: {
    backgroundColor: theme.colors.primary[500],
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    ...theme.shadows.base,
  },
  disabledButton: {
    backgroundColor: theme.colors.neutral[300],
  },
  continueButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.inverse,
  },
  disabledButtonText: {
    color: theme.colors.text.disabled,
  },
  footerNote: {
    fontSize: 12,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default RoleSelectionScreen;
