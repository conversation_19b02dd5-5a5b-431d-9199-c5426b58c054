import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing, borderRadius, shadows } from '../../constants/theme';

const { width: screenWidth } = Dimensions.get('window');

interface OnboardingSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  gradient: readonly [string, string, ...string[]];
  illustration: string;
}

const onboardingData: OnboardingSlide[] = [
  {
    id: 1,
    title: 'Bienvenue sur Mientior',
    subtitle: 'Livraison Rapide en Afrique',
    description: 'Découvrez la plateforme de livraison qui connecte clients, marchands et livreurs à travers l\'Afrique.',
    icon: 'rocket-outline',
    gradient: [colors.primary[400], colors.primary[600]] as const,
    illustration: '🚀',
  },
  {
    id: 2,
    title: 'Commandez Facilement',
    subtitle: 'Produits Locaux & Authentiques',
    description: 'Explorez une large gamme de produits locaux et authentiques proposés par nos marchands partenaires.',
    icon: 'storefront-outline',
    gradient: [colors.secondary[400], colors.secondary[600]] as const,
    illustration: '🛍️',
  },
  {
    id: 3,
    title: 'Livraison Express',
    subtitle: 'Suivi en Temps Réel',
    description: 'Suivez vos commandes en temps réel et recevez vos produits rapidement grâce à notre réseau de livreurs.',
    icon: 'bicycle-outline',
    gradient: [colors.accent[400], colors.accent[600]] as const,
    illustration: '🚴‍♂️',
  },
  {
    id: 4,
    title: 'Rejoignez-nous',
    subtitle: 'Client, Marchand ou Livreur',
    description: 'Choisissez votre rôle et commencez votre aventure avec Mientior dès aujourd\'hui !',
    icon: 'people-outline',
    gradient: [colors.primary[500], colors.secondary[500]] as const,
    illustration: '🤝',
  },
];

interface OnboardingScreenProps {
  navigation: any;
}

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ navigation }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      scrollViewRef.current?.scrollTo({
        x: nextIndex * screenWidth,
        animated: true,
      });
      animateTransition();
    } else {
      handleGetStarted();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      scrollViewRef.current?.scrollTo({
        x: prevIndex * screenWidth,
        animated: true,
      });
      animateTransition();
    }
  };

  const handleGetStarted = () => {
    navigation.navigate('LanguageSelection');
  };

  const handleSkip = () => {
    navigation.navigate('LanguageSelection');
  };

  const handleTestSupabase = () => {
    navigation.navigate('SupabaseTest');
  };

  const animateTransition = () => {
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0.7,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 150,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  };

  const onScroll = (event: any) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
    if (slideIndex !== currentIndex) {
      setCurrentIndex(slideIndex);
    }
  };

  const renderSlide = (item: OnboardingSlide) => (
    <View key={item.id} style={styles.slide}>
      <LinearGradient
        colors={item.gradient}
        style={styles.gradientBackground}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* Illustration */}
          <View style={styles.illustrationContainer}>
            <Text style={styles.illustration}>{item.illustration}</Text>
            <View style={styles.iconContainer}>
              <Ionicons name={item.icon} size={40} color={colors.text.inverse} />
            </View>
          </View>

          {/* Contenu textuel */}
          <View style={styles.textContainer}>
            <Text style={styles.title}>{item.title}</Text>
            <Text style={styles.subtitle}>{item.subtitle}</Text>
            <Text style={styles.description}>{item.description}</Text>
          </View>

          {/* Motifs décoratifs africains */}
          <View style={styles.decorativeElements}>
            <View style={[styles.decorativeCircle, styles.circle1]} />
            <View style={[styles.decorativeCircle, styles.circle2]} />
            <View style={[styles.decorativeCircle, styles.circle3]} />
          </View>
        </Animated.View>
      </LinearGradient>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {onboardingData.map((_, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.paginationDot,
            index === currentIndex ? styles.paginationDotActive : styles.paginationDotInactive,
          ]}
          onPress={() => {
            setCurrentIndex(index);
            scrollViewRef.current?.scrollTo({
              x: index * screenWidth,
              animated: true,
            });
          }}
        />
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header avec boutons Skip et Test */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleTestSupabase} style={styles.testButton}>
          <Ionicons name="flask" size={16} color={colors.secondary[500]} />
          <Text style={styles.testText}>Test DB</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
          <Text style={styles.skipText}>Passer</Text>
        </TouchableOpacity>
      </View>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={onScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
      >
        {onboardingData.map((item) => renderSlide(item))}
      </ScrollView>

      {/* Footer avec pagination et boutons */}
      <View style={styles.footer}>
        {renderPagination()}

        <View style={styles.buttonContainer}>
          {currentIndex > 0 && (
            <TouchableOpacity onPress={handlePrevious} style={styles.secondaryButton}>
              <Ionicons name="chevron-back" size={20} color={colors.primary[500]} />
              <Text style={styles.secondaryButtonText}>Précédent</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity onPress={handleNext} style={styles.primaryButton}>
            <Text style={styles.primaryButtonText}>
              {currentIndex === onboardingData.length - 1 ? 'Commencer' : 'Suivant'}
            </Text>
            <Ionicons
              name={currentIndex === onboardingData.length - 1 ? "checkmark" : "chevron-forward"}
              size={20}
              color={colors.text.inverse}
            />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.sm,
    borderWidth: 1,
    borderColor: colors.secondary[500],
  },
  testText: {
    fontSize: typography.fontSize.sm,
    color: colors.secondary[500],
    fontWeight: typography.fontWeight.medium,
    marginLeft: spacing.xs,
  },
  skipButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  skipText: {
    fontSize: typography.fontSize.base,
    color: colors.text.secondary,
    fontWeight: '500' as const,
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    width: screenWidth,
    flex: 1,
  },
  gradientBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  illustrationContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  illustration: {
    fontSize: 120,
    marginBottom: spacing.lg,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.md,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: '700' as const,
    color: colors.text.inverse,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: '500' as const,
    color: colors.text.inverse,
    textAlign: 'center',
    marginBottom: spacing.md,
    opacity: 0.9,
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.text.inverse,
    textAlign: 'center',
    lineHeight: typography.lineHeight.relaxed * typography.fontSize.base,
    opacity: 0.8,
    maxWidth: 300,
  },
  decorativeElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  decorativeCircle: {
    position: 'absolute',
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  circle1: {
    width: 100,
    height: 100,
    top: '10%',
    left: '10%',
  },
  circle2: {
    width: 60,
    height: 60,
    top: '20%',
    right: '15%',
  },
  circle3: {
    width: 80,
    height: 80,
    bottom: '15%',
    left: '20%',
  },
  footer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    backgroundColor: colors.background.primary,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  paginationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: spacing.xs,
  },
  paginationDotActive: {
    backgroundColor: colors.primary[500],
    width: 24,
  },
  paginationDotInactive: {
    backgroundColor: colors.neutral[300],
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary[500],
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    ...shadows.base,
    flex: 1,
    justifyContent: 'center',
    marginLeft: spacing.md,
  },
  primaryButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: '600' as const,
    color: colors.text.inverse,
    marginRight: spacing.sm,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  secondaryButtonText: {
    fontSize: typography.fontSize.base,
    fontWeight: '500' as const,
    color: colors.primary[500],
    marginLeft: spacing.sm,
  },
});

export default OnboardingScreen;
