import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import * as Location from 'expo-location';
import { theme } from '../../constants/theme';

export const LocationPermissionScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);

  const handleRequestPermission = async () => {
    try {
      setLoading(true);

      // Demander la permission de géolocalisation
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        // Permission accordée, obtenir la position actuelle
        try {
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Balanced,
          });
          
          console.log('Position obtenue:', location.coords);
          
          Alert.alert(
            'Permission accordée',
            'Parfait ! Nous pourrons vous proposer les meilleurs restaurants près de chez vous.',
            [{ text: 'Continuer', onPress: () => navigation.navigate('Auth' as never) }]
          );
        } catch (locationError) {
          console.error('Erreur lors de l\'obtention de la position:', locationError);
          // Même si on ne peut pas obtenir la position, on continue
          navigation.navigate('Auth' as never);
        }
      } else {
        // Permission refusée
        Alert.alert(
          'Permission refusée',
          'Vous pourrez toujours utiliser l\'application en saisissant votre adresse manuellement.',
          [{ text: 'Continuer', onPress: () => navigation.navigate('Auth' as never) }]
        );
      }
    } catch (error) {
      console.error('Erreur lors de la demande de permission:', error);
      Alert.alert(
        'Erreur',
        'Une erreur est survenue. Vous pourrez configurer la géolocalisation plus tard.',
        [{ text: 'Continuer', onPress: () => navigation.navigate('Auth' as never) }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    Alert.alert(
      'Ignorer la géolocalisation',
      'Vous pourrez activer la géolocalisation plus tard dans les paramètres pour une meilleure expérience.',
      [
        { text: 'Retour', style: 'cancel' },
        { text: 'Ignorer', onPress: () => navigation.navigate('Auth' as never) }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={theme.colors.primary[500]} barStyle="light-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Text style={styles.locationIcon}>📍</Text>
        </View>
        <Text style={styles.title}>Localisation</Text>
        <Text style={styles.subtitle}>
          Autorisez l'accès à votre position pour une meilleure expérience
        </Text>
      </View>

      {/* Contenu principal */}
      <View style={styles.content}>
        {/* Illustration */}
        <View style={styles.illustrationContainer}>
          <Image 
            source={require('../../../assets/icon.png')} 
            style={styles.illustration}
            resizeMode="contain"
          />
        </View>

        {/* Avantages */}
        <View style={styles.benefitsContainer}>
          <Text style={styles.benefitsTitle}>Pourquoi autoriser la géolocalisation ?</Text>
          
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>🎯</Text>
            <Text style={styles.benefitText}>
              Trouvez les restaurants et boutiques les plus proches de vous
            </Text>
          </View>
          
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>⚡</Text>
            <Text style={styles.benefitText}>
              Livraisons plus rapides avec des temps d'attente réduits
            </Text>
          </View>
          
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>💰</Text>
            <Text style={styles.benefitText}>
              Frais de livraison optimisés selon votre distance
            </Text>
          </View>
          
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>📱</Text>
            <Text style={styles.benefitText}>
              Suivi en temps réel de vos commandes et livreurs
            </Text>
          </View>
        </View>
      </View>

      {/* Boutons */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.allowButton, loading && styles.buttonDisabled]}
          onPress={handleRequestPermission}
          disabled={loading}
          activeOpacity={0.8}
        >
          {loading ? (
            <ActivityIndicator color={theme.colors.text.inverse} size="small" />
          ) : (
            <Text style={styles.allowButtonText}>Autoriser la géolocalisation</Text>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.skipButton}
          onPress={handleSkip}
          disabled={loading}
          activeOpacity={0.8}
        >
          <Text style={styles.skipButtonText}>Plus tard</Text>
        </TouchableOpacity>
        
        <Text style={styles.privacyNote}>
          🔒 Vos données de localisation sont sécurisées et ne sont utilisées que pour améliorer votre expérience
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  header: {
    backgroundColor: theme.colors.primary[500],
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 24,
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.primary[400],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationIcon: {
    fontSize: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text.inverse,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text.inverse,
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 40,
  },
  illustrationContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  illustration: {
    width: 120,
    height: 120,
    opacity: 0.8,
  },
  benefitsContainer: {
    flex: 1,
  },
  benefitsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: 24,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
    paddingHorizontal: 8,
  },
  benefitIcon: {
    fontSize: 24,
    marginRight: 16,
    marginTop: 2,
  },
  benefitText: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text.secondary,
    lineHeight: 24,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
    paddingTop: 20,
  },
  allowButton: {
    backgroundColor: theme.colors.primary[500],
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    ...theme.shadows.base,
  },
  buttonDisabled: {
    backgroundColor: theme.colors.neutral[300],
    shadowOpacity: 0,
    elevation: 0,
  },
  allowButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.inverse,
  },
  skipButton: {
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  privacyNote: {
    fontSize: 12,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 18,
    paddingHorizontal: 16,
  },
});

export default LocationPermissionScreen;
