import { useState, useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { useAuth } from './useAuth';

interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: any;
  timestamp: Date;
  read: boolean;
  type: 'order' | 'delivery' | 'promotion' | 'system';
}

interface NotificationState {
  notifications: NotificationData[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  expoPushToken: string | null;
  hasPermission: boolean;
}

// Configuration des notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export const useNotifications = () => {
  const { user } = useAuth();
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
    expoPushToken: null,
    hasPermission: false,
  });

  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    if (user) {
      initializeNotifications();
      loadNotifications();
    }

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, [user]);

  const initializeNotifications = async () => {
    try {
      // Demander les permissions pour les notifications locales
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Permission de notification refusée');
        setState(prev => ({ ...prev, hasPermission: false }));
        return;
      }

      setState(prev => ({ ...prev, hasPermission: true }));

      // Configuration pour Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Notifications Mientior',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          sound: 'default',
        });

        // Canal pour les commandes
        await Notifications.setNotificationChannelAsync('orders', {
          name: 'Commandes',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          sound: 'default',
        });

        // Canal pour les livraisons
        await Notifications.setNotificationChannelAsync('delivery', {
          name: 'Livraisons',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 500, 250, 500],
          sound: 'default',
        });
      }

      // Écouter les notifications reçues
      notificationListener.current = Notifications.addNotificationReceivedListener(
        handleNotificationReceived
      );

      // Écouter les interactions avec les notifications
      responseListener.current = Notifications.addNotificationResponseReceivedListener(
        handleNotificationResponse
      );

      // Note: Les push notifications ne sont pas disponibles dans Expo Go
      // Elles nécessitent un development build
      console.log('Notifications locales initialisées avec succès');

    } catch (error) {
      console.error('Erreur lors de l\'initialisation des notifications:', error);
      setState(prev => ({ 
        ...prev, 
        error: 'Erreur lors de l\'initialisation des notifications' 
      }));
    }
  };

  const handleNotificationReceived = (notification: Notifications.Notification) => {
    const notificationData: NotificationData = {
      id: notification.request.identifier,
      title: notification.request.content.title || '',
      body: notification.request.content.body || '',
      data: notification.request.content.data,
      timestamp: new Date(),
      read: false,
      type: (notification.request.content.data?.type as NotificationData['type']) || 'system',
    };

    setState(prev => ({
      ...prev,
      notifications: [notificationData, ...prev.notifications],
      unreadCount: prev.unreadCount + 1,
    }));
  };

  const handleNotificationResponse = (response: Notifications.NotificationResponse) => {
    const notification = response.notification;
    const data = notification.request.content.data;

    // Gérer les actions basées sur le type de notification
    if (data?.type === 'order' && data?.orderId) {
      // Naviguer vers l'écran de commande
      console.log('Navigation vers commande:', data.orderId);
    } else if (data?.type === 'delivery' && data?.deliveryId) {
      // Naviguer vers le tracking
      console.log('Navigation vers tracking:', data.deliveryId);
    }

    // Marquer comme lu
    markAsRead(notification.request.identifier);
  };

  const loadNotifications = async () => {
    if (!user) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      // Simuler des notifications pour le développement
      const mockNotifications: NotificationData[] = [
        {
          id: '1',
          title: 'Commande confirmée',
          body: 'Votre commande #12345 a été confirmée',
          timestamp: new Date(),
          read: false,
          type: 'order',
        },
        {
          id: '2',
          title: 'Livraison en cours',
          body: 'Votre commande est en route',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          read: true,
          type: 'delivery',
        },
      ];

      const unreadCount = mockNotifications.filter(n => !n.read).length;

      setState(prev => ({
        ...prev,
        notifications: mockNotifications,
        unreadCount,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Erreur lors du chargement des notifications',
        loading: false,
      }));
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n =>
          n.id === notificationId ? { ...n, read: true } : n
        ),
        unreadCount: Math.max(0, prev.unreadCount - 1),
      }));
    } catch (error) {
      console.error('Erreur lors du marquage comme lu:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!user) return;

    try {
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => ({ ...n, read: true })),
        unreadCount: 0,
      }));
    } catch (error) {
      console.error('Erreur lors du marquage de toutes les notifications:', error);
    }
  };

  const sendLocalNotification = async (
    title: string,
    body: string,
    data?: any,
    channelId: string = 'default'
  ) => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: null, // Immédiatement
        identifier: `local_${Date.now()}`,
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi de notification locale:', error);
    }
  };

  const scheduledNotification = async (
    title: string,
    body: string,
    triggerDate: Date,
    data?: any
  ) => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: {
          channelId: 'default',
          date: triggerDate,
        },
        identifier: `scheduled_${Date.now()}`,
      });
    } catch (error) {
      console.error('Erreur lors de la programmation de notification:', error);
    }
  };

  const cancelNotification = async (identifier: string) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
    } catch (error) {
      console.error('Erreur lors de l\'annulation de notification:', error);
    }
  };

  const setBadgeCount = async (count: number) => {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du badge:', error);
    }
  };

  const getNotificationsByType = (type: NotificationData['type']) => {
    return state.notifications.filter(n => n.type === type);
  };

  const getUnreadNotifications = () => {
    return state.notifications.filter(n => !n.read);
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      setState(prev => ({
        ...prev,
        notifications: prev.notifications.filter(n => n.id !== notificationId),
        unreadCount: prev.notifications.find(n => n.id === notificationId && !n.read) 
          ? prev.unreadCount - 1 
          : prev.unreadCount,
      }));
    } catch (error) {
      console.error('Erreur lors de la suppression de notification:', error);
    }
  };

  const clearAllNotifications = () => {
    setState(prev => ({
      ...prev,
      notifications: [],
      unreadCount: 0,
    }));
  };

  return {
    ...state,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    sendLocalNotification,
    scheduledNotification,
    cancelNotification,
    setBadgeCount,
    getNotificationsByType,
    getUnreadNotifications,
    deleteNotification,
    clearAllNotifications,
    refresh: loadNotifications,
    registerForPushNotifications: async () => {
      console.log('🔔 Initialisation des notifications...');
      return state.expoPushToken;
    },
  };
};
