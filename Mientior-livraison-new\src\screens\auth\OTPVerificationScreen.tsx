import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { theme } from '../../constants/theme';

interface OTPVerificationScreenProps {
  phone: string;
  email: string;
}

export const OTPVerificationScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { phone, email } = route.params as OTPVerificationScreenProps;

  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    // Démarrer le timer de renvoi
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) return; // Empêcher la saisie de plusieurs caractères

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Passer au champ suivant automatiquement
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = async () => {
    const otpCode = otp.join('');
    
    if (otpCode.length !== 6) {
      Alert.alert('Erreur', 'Veuillez entrer le code à 6 chiffres complet');
      return;
    }

    try {
      setLoading(true);
      
      // TODO: Implémenter la vérification OTP avec Supabase
      // Pour l'instant, simuler la vérification
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simuler une vérification réussie
      if (otpCode === '123456') {
        Alert.alert(
          'Vérification réussie',
          'Votre compte a été vérifié avec succès !',
          [{ text: 'OK', onPress: () => navigation.navigate('RoleSelection' as never) }]
        );
      } else {
        Alert.alert('Erreur', 'Code de vérification incorrect. Veuillez réessayer.');
        setOtp(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
      }
    } catch (error) {
      Alert.alert('Erreur', 'Une erreur est survenue lors de la vérification.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    try {
      setResendLoading(true);
      
      // TODO: Implémenter le renvoi d'OTP
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Alert.alert('Code renvoyé', 'Un nouveau code de vérification a été envoyé.');
      
      // Réinitialiser le timer
      setTimer(60);
      setCanResend(false);
      
      // Redémarrer le timer
      const interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de renvoyer le code. Veuillez réessayer.');
    } finally {
      setResendLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Vérification</Text>
          <Text style={styles.subtitle}>
            Entrez le code à 6 chiffres envoyé au
          </Text>
          <Text style={styles.phoneNumber}>{phone}</Text>
        </View>

        {/* Champs OTP */}
        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => {
                if (ref) inputRefs.current[index] = ref;
              }}
              style={[
                styles.otpInput,
                digit && styles.otpInputFilled
              ]}
              value={digit}
              onChangeText={(value) => handleOtpChange(value, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="numeric"
              maxLength={1}
              selectTextOnFocus
              editable={!loading}
            />
          ))}
        </View>

        {/* Bouton de vérification */}
        <TouchableOpacity
          style={[
            styles.verifyButton,
            loading && styles.buttonDisabled
          ]}
          onPress={handleVerifyOTP}
          disabled={loading || otp.join('').length !== 6}
        >
          {loading ? (
            <ActivityIndicator color={theme.colors.text.inverse} size="small" />
          ) : (
            <Text style={styles.verifyButtonText}>Vérifier</Text>
          )}
        </TouchableOpacity>

        {/* Renvoi du code */}
        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>
            Vous n'avez pas reçu le code ?
          </Text>
          
          {canResend ? (
            <TouchableOpacity
              onPress={handleResendOTP}
              disabled={resendLoading}
              style={styles.resendButton}
            >
              {resendLoading ? (
                <ActivityIndicator color={theme.colors.primary[500]} size="small" />
              ) : (
                <Text style={styles.resendButtonText}>Renvoyer</Text>
              )}
            </TouchableOpacity>
          ) : (
            <Text style={styles.timerText}>
              Renvoyer dans {formatTime(timer)}
            </Text>
          )}
        </View>

        {/* Option rappel vocal */}
        <TouchableOpacity
          style={styles.voiceCallButton}
          onPress={() => Alert.alert('Rappel vocal', 'Cette fonctionnalité sera bientôt disponible.')}
        >
          <Text style={styles.voiceCallText}>
            📞 Recevoir un appel vocal
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: 4,
  },
  phoneNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary[500],
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderColor: theme.colors.border.light,
    borderRadius: 12,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
    backgroundColor: theme.colors.surface.primary,
  },
  otpInputFilled: {
    borderColor: theme.colors.primary[500],
    backgroundColor: theme.colors.primary[50],
  },
  verifyButton: {
    backgroundColor: theme.colors.primary[500],
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 30,
    ...theme.shadows.base,
  },
  buttonDisabled: {
    backgroundColor: theme.colors.neutral[300],
    shadowOpacity: 0,
    elevation: 0,
  },
  verifyButtonText: {
    color: theme.colors.text.inverse,
    fontSize: 18,
    fontWeight: '600',
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  resendText: {
    fontSize: 14,
    color: theme.colors.text.secondary,
    marginBottom: 8,
  },
  resendButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  resendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary[500],
  },
  timerText: {
    fontSize: 14,
    color: theme.colors.text.secondary,
    fontWeight: '500',
  },
  voiceCallButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  voiceCallText: {
    fontSize: 14,
    color: theme.colors.text.secondary,
    textDecorationLine: 'underline',
  },
});

export default OTPVerificationScreen;
